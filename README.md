# Bezeq Project

This project processes PDF documents using Azure AI Document Intelligence and provides data analysis capabilities.

## Setup

1. Create a virtual environment:
   ```bash
   python -m venv .venv
   ```

2. Activate the virtual environment:
   ```bash
   # Windows
   .venv\Scripts\activate

   # Linux/Mac
   source .venv/bin/activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements_clean.txt
   ```

4. Configure the application:
   - Copy `config/config.ini.example` to `config/config.ini` (if exists)
   - Update the configuration with your Azure credentials and settings

## Project Structure

- `src/` - Source code modules
  - `database/` - Database related functionality
  - `excel_analysis/` - Excel file analysis tools
  - `models/` - Data models
  - `pdf_processing/` - PDF processing utilities
  - `utils/` - Utility functions
- `config/` - Configuration files
- `data/` - Data directory (excluded from Git)
  - `pdfs/` - Input PDF files
  - `processed_data/` - Processed output
  - `raw_json/` - Raw JSON data
  - `splitted_pdfs/` - Split PDF files

## Usage

Run the main application:
```bash
python main.py
```

For query examples:
```bash
python query_example.py
```

## Notes

- The `data/` directory is excluded from version control as it contains processed files and potentially sensitive data
- Configuration files with sensitive information are excluded from Git
- Make sure to configure your Azure AI Document Intelligence credentials before running